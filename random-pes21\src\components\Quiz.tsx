import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { User, Users, Image, Languages } from 'lucide-react';
import { BackgroundImage } from '../types';

// TypeScript interfaces for quiz data
interface AnswerOption {
  answerText: string;
  isCorrect: boolean;
}

interface QuizQuestion {
  questionText: string;
  answerOptions: AnswerOption[];
}

interface ChatMessage {
  role: string;
  parts: Array<{ text: string }>;
}

// Categories for the quiz
const categories = [
  'World Cup',
  'Champions League',
  'Player Trivia',
  'Football Rules',
  'Club Football',
  'International Football',
];

const GEMINI_API_KEY = 'AIzaSyCwaY5RqPR9llMopkMH9xc1pM2EwlSgBKM';
const BACKGROUND_STORAGE_KEY = 'selectedBackground';
const QUIZ_BACKGROUND_TOGGLE_KEY = 'quizUseBackground';
const QUIZ_LANGUAGE_KEY = 'quizLanguage';

// Language options
const languages = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'ar', name: 'العربية', flag: '🇸🇦' }
];

const Quiz: React.FC = () => {
  // State for the current question index
  const [currentQuestion, setCurrentQuestion] = useState(0);
  // State for the user's score
  const [score, setScore] = useState(0);
  // State to track if the quiz has ended
  const [showScore, setShowScore] = useState(false);
  // State for the currently selected answer
  const [selectedAnswer, setSelectedAnswer] = useState<null | boolean>(null);
  // State for feedback message (e.g., "Correct!", "Incorrect!")
  const [feedbackMessage, setFeedbackMessage] = useState('');
  // State to disable answer options after selection
  const [answersDisabled, setAnswersDisabled] = useState(false);
  // State for LLM generated explanation
  const [explanation, setExplanation] = useState('');
  // State for loading status of LLM explanation
  const [loadingExplanation, setLoadingExplanation] = useState(false);
  // State for the selected category
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  // State for the dynamically loaded quiz questions
  const [quizQuestions, setQuizQuestions] = useState<QuizQuestion[]>([]);
  // State for loading status of quiz questions
  const [loadingQuestions, setLoadingQuestions] = useState(false);
  // State for error message when fetching questions
  const [questionsError, setQuestionsError] = useState('');
  // State for custom category input
  const [customCategoryInput, setCustomCategoryInput] = useState('');
  // State for quiz mode selection
  const [selectedMode, setSelectedMode] = useState<string | null>(null);
  // State for background management
  const [background, setBackground] = useState<BackgroundImage | null>(null);
  // State for toggling background usage in quiz
  const [useBackground, setUseBackground] = useState<boolean>(true);
  // State for language selection
  const [selectedLanguage, setSelectedLanguage] = useState<string>('en');

  // Load background and toggle state from localStorage
  useEffect(() => {
    // Load saved background from localStorage
    const savedBackground = localStorage.getItem(BACKGROUND_STORAGE_KEY);
    if (savedBackground) {
      setBackground(JSON.parse(savedBackground));
    }

    // Load background toggle preference from localStorage
    const savedToggle = localStorage.getItem(QUIZ_BACKGROUND_TOGGLE_KEY);
    if (savedToggle !== null) {
      setUseBackground(JSON.parse(savedToggle));
    }

    // Load language preference from localStorage
    const savedLanguage = localStorage.getItem(QUIZ_LANGUAGE_KEY);
    if (savedLanguage) {
      setSelectedLanguage(savedLanguage);
    }
  }, []);

  // Function to toggle background usage
  const toggleBackgroundUsage = () => {
    const newUseBackground = !useBackground;
    setUseBackground(newUseBackground);
    localStorage.setItem(QUIZ_BACKGROUND_TOGGLE_KEY, JSON.stringify(newUseBackground));
  };

  // Function to toggle language
  const toggleLanguage = () => {
    const newLanguage = selectedLanguage === 'en' ? 'ar' : 'en';
    setSelectedLanguage(newLanguage);
    localStorage.setItem(QUIZ_LANGUAGE_KEY, newLanguage);
  };

  // Function to handle answer option clicks
  const handleAnswerOptionClick = (isCorrect: boolean) => {
    if (answersDisabled) return;
    setSelectedAnswer(isCorrect);
    setAnswersDisabled(true);
    if (isCorrect) {
      setScore(score + 1);
      setFeedbackMessage('Correct!');
    } else {
      setFeedbackMessage('Incorrect!');
    }
  };

  // Function to move to the next question
  const handleNextQuestion = () => {
    const nextQuestion = currentQuestion + 1;
    if (nextQuestion < quizQuestions.length) {
      setCurrentQuestion(nextQuestion);
      setSelectedAnswer(null);
      setFeedbackMessage('');
      setAnswersDisabled(false);
      setExplanation('');
    } else {
      setShowScore(true);
    }
  };

  // Function to reset the quiz and go back to category selection
  const resetQuizAndGoToCategories = () => {
    setCurrentQuestion(0);
    setScore(0);
    setShowScore(false);
    setSelectedAnswer(null);
    setFeedbackMessage('');
    setAnswersDisabled(false);
    setExplanation('');
    setQuizQuestions([]);
    setSelectedCategory(null);
    setQuestionsError('');
    setCustomCategoryInput('');
    setSelectedMode(null);
  };

  // Function to get explanation from Gemini API
  const handleExplainAnswer = async () => {
    setLoadingExplanation(true);
    setExplanation('');
    const currentQuestionData = quizQuestions[currentQuestion];
    const correctAnswer = currentQuestionData.answerOptions.find((opt: AnswerOption) => opt.isCorrect)?.answerText;
    const prompt = `Explain the correct answer to the following football trivia question: "${currentQuestionData.questionText}". The correct answer is "${correctAnswer}". Provide a concise explanation.`;
    const chatHistory: ChatMessage[] = [];
    chatHistory.push({ role: "user", parts: [{ text: prompt }] });
    const payload = { contents: chatHistory };
    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${GEMINI_API_KEY}`;
    try {
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });
      const result = await response.json();
      if (result.candidates && result.candidates.length > 0 &&
          result.candidates[0].content && result.candidates[0].content.parts &&
          result.candidates[0].content.parts.length > 0 && result.candidates[0].content.parts[0].text) {
        const text = result.candidates[0].content.parts[0].text;
        setExplanation(text);
      } else {
        setExplanation('Could not get an explanation. Please try again.');
      }
    } catch {
      setExplanation('Failed to fetch explanation. Network error or API issue.');
    } finally {
      setLoadingExplanation(false);
    }
  };

  // Function to fetch questions from Gemini API based on category
  const fetchQuestions = async (category: string) => {
    if (!category.trim()) {
      setQuestionsError('Category cannot be empty.');
      return;
    }
    setSelectedCategory(category);
    setLoadingQuestions(true);
    setQuizQuestions([]);
    setQuestionsError('');
    setCurrentQuestion(0);
    setScore(0);
    setShowScore(false);
    setSelectedAnswer(null);
    setFeedbackMessage('');
    setAnswersDisabled(false);
    setExplanation('');
    const prompt = `Generate 10 football trivia questions about ${category} in JSON format. Each question should have a "questionText" and an "answerOptions" array. Each answer option should have "answerText" and a "isCorrect" boolean. Ensure there is exactly one correct answer per question.`;
    const chatHistory: ChatMessage[] = [];
    chatHistory.push({ role: "user", parts: [{ text: prompt }] });
    const payload = {
      contents: chatHistory,
      generationConfig: {
        responseMimeType: "application/json",
        responseSchema: {
          type: "ARRAY",
          items: {
            type: "OBJECT",
            properties: {
              "questionText": { "type": "STRING" },
              "answerOptions": {
                "type": "ARRAY",
                "items": {
                  "type": "OBJECT",
                  "properties": {
                    "answerText": { "type": "STRING" },
                    "isCorrect": { "type": "BOOLEAN" }
                  },
                  "required": ["answerText", "isCorrect"]
                }
              }
            },
            "required": ["questionText", "answerOptions"]
          }
        }
      }
    };
    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${GEMINI_API_KEY}`;
    try {
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });
      const result = await response.json();
      if (result.candidates && result.candidates.length > 0 &&
          result.candidates[0].content && result.candidates[0].content.parts &&
          result.candidates[0].content.parts.length > 0) {
        const jsonText = result.candidates[0].content.parts[0].text;
        try {
          const parsedQuestions = JSON.parse(jsonText);
          if (Array.isArray(parsedQuestions) && parsedQuestions.length > 0) {
            setQuizQuestions(parsedQuestions);
          } else {
            setQuestionsError('Received invalid question format from API. Please try again.');
          }
        } catch {
          setQuestionsError('Failed to parse questions from API. Please try again.');
        }
      } else {
        setQuestionsError('Could not generate questions. Please try again.');
      }
    } catch {
      setQuestionsError('Failed to fetch questions. Network error or API issue.');
    } finally {
      setLoadingQuestions(false);
    }
  };

  const handleCustomCategorySubmit = () => {
    fetchQuestions(customCategoryInput);
  };

  // Create container style based on background and toggle state
  const containerStyle = (background && useBackground) ? {
    backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url(${background.url})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center'
  } : {};

  // Determine container classes based on background usage
  const containerClasses = (background && useBackground)
    ? "min-h-screen flex items-center justify-center p-4 font-inter transition-all duration-500"
    : "min-h-screen bg-gradient-to-br from-green-500 to-blue-600 flex items-center justify-center p-4 font-inter";

  return (
    <div className={containerClasses} style={containerStyle}>
      <Link to="/" className="absolute top-4 left-4 bg-gradient-to-r from-yellow-400 to-yellow-200 text-black font-bold py-2 px-5 rounded-full shadow-md hover:scale-105 transition-all duration-300 border-2 border-yellow-500">← Home</Link>

      {/* Language switcher button */}
      <button
        onClick={toggleLanguage}
        className="absolute top-4 right-16 bg-gradient-to-r from-purple-400 to-purple-600 text-white p-2 rounded-full shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300 z-10 flex items-center gap-1"
        title={`Switch to ${selectedLanguage === 'en' ? 'Arabic' : 'English'}`}
      >
        <Languages size={16} />
        <span className="text-sm font-medium">
          {languages.find(lang => lang.code === selectedLanguage)?.flag}
        </span>
      </button>

      {/* Background toggle button in top right corner */}
      {background && (
        <button
          onClick={toggleBackgroundUsage}
          className="absolute top-4 right-4 bg-gradient-to-r from-blue-400 to-blue-600 text-white p-2 rounded-full shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300 z-10"
          title={useBackground ? "Disable background" : "Enable background"}
        >
          <Image size={20} />
        </button>
      )}
      <div className="bg-white rounded-3xl shadow-2xl p-8 w-full max-w-xl border-4 border-yellow-300 relative">
        <div className="text-center mb-8">
          <h2 className="text-4xl md:text-5xl font-extrabold gradient-red-blue inline-block text-transparent bg-clip-text mb-2">Football Quiz</h2>
          <p className="text-lg text-gray-600 font-medium">Test your football knowledge!</p>
        </div>
        {/* Mode Selection */}
        {!selectedMode && (
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-6">Choose Mode</h2>
            <div className="flex flex-col gap-4 justify-center items-center">
              <button
                onClick={() => setSelectedMode('Single Player')}
                className="py-3 px-6 text-lg font-bold rounded-full shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300 focus:outline-none flex items-center justify-center gradient-yellow"
                style={{ minWidth: '200px' }}
              >
                <User size={20} className="mr-2" />
                Single Player
              </button>
              <button
                onClick={() => setSelectedMode('Multi-Player')}
                className="py-3 px-6 text-lg font-bold rounded-full shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300 focus:outline-none flex items-center justify-center bg-gray-200 text-gray-800 hover:bg-gray-300"
                style={{ minWidth: '200px' }}
              >
                <Users size={20} className="mr-2" />
                Multi-Player
              </button>
            </div>
          </div>
        )}
        {/* Multi-Player Coming Soon */}
        {selectedMode === 'Multi-Player' && (
          <div className="text-center mt-12">
            <h2 className="text-3xl font-bold text-gray-800 mb-4">Multi-Player</h2>
            <p className="text-xl text-gray-700 mb-6">Coming Soon 🚧</p>
            <button
              onClick={() => setSelectedMode(null)}
              className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-6 rounded-xl transition duration-300 ease-in-out shadow-lg"
            >
              Back
            </button>
          </div>
        )}
        {/* Single Player Quiz Logic */}
        {selectedMode === 'Single Player' && (
          !selectedCategory || quizQuestions.length === 0 && !loadingQuestions && !questionsError ? (
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-800 mb-6">Choose a Football Category</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                {categories.map((category) => (
                  <button
                    key={category}
                    onClick={() => fetchQuestions(category)}
                    className="font-bold py-3 px-6 rounded-xl transition duration-300 ease-in-out transform hover:scale-105 shadow-lg text-yellow-100 border-yellow-300"
                    style={{ background: 'linear-gradient(110deg, var(--barça-blue) 50%, var(--barça-red) 50%)' }}
                  >
                    {category}
                  </button>
                ))}
              </div>
              <div className="mt-8">
                <h3 className="text-xl font-bold text-gray-800 mb-4">Or Enter Your Own Category</h3>
                <input
                  type="text"
                  value={customCategoryInput}
                  onChange={(e) => setCustomCategoryInput(e.target.value)}
                  placeholder="e.g., 'Premier League History'"
                  className="w-full p-3 border-2 border-gray-300 rounded-xl focus:outline-none focus:border-blue-500 mb-4 text-gray-800"
                />
                <button
                  onClick={handleCustomCategorySubmit}
                  className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold py-3 px-6 rounded-xl transition duration-300 ease-in-out transform hover:scale-105 shadow-lg border-2"
                  disabled={!customCategoryInput.trim()}
                >
                  Start Custom Quiz
                </button>
              </div>
              {questionsError && (
                <p className="text-red-500 mt-4 text-sm">{questionsError}</p>
              )}
              <button
                onClick={() => setSelectedMode(null)}
                className="w-full mt-4 text-gray-700 font-bold py-2 px-6 rounded-xl transition duration-300"
              >
                Back to Select Mode
              </button>
            </div>
          ) : loadingQuestions ? (
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-800 mb-4">Loading Questions...</h2>
              <p className="text-gray-600">Please wait while we fetch your trivia questions.</p>
              <div className="flex justify-center items-center mt-6">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
              </div>
            </div>
          ) : showScore ? (
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-800 mb-4">Quiz Completed!</h2>
              <p className="text-xl text-gray-700 mb-6">
                You scored <span className="font-bold text-purple-700">{score}</span> out of <span className="font-bold text-purple-700">{quizQuestions.length}</span>
              </p>
              <button
                onClick={resetQuizAndGoToCategories}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-xl transition duration-300 ease-in-out transform hover:scale-105 shadow-lg mb-4"
              >
                Play Again / Choose Category
              </button>
              <Link to="/" className="w-full block bg-yellow-400 hover:bg-yellow-500 text-black font-bold py-3 px-6 rounded-xl transition duration-300 ease-in-out transform hover:scale-105 shadow-lg border-2 border-yellow-500">Back to Home</Link>
            </div>
          ) : (
            <div>
              <div className="flex justify-between items-center mb-6">
                <div className="text-lg font-semibold text-gray-600">
                  Question <span className="font-bold text-blue-700">{currentQuestion + 1}</span> / {quizQuestions.length}
                </div>
                <button
                  onClick={resetQuizAndGoToCategories}
                  className="bg-red-500 hover:bg-red-600 text-white text-sm font-bold py-2 px-4 rounded-xl transition duration-300 ease-in-out shadow-md"
                >
                  Cancel Quiz
                </button>
              </div>
              <div className="text-2xl font-bold text-gray-800 mb-6 bg-gradient-to-r from-blue-100 to-purple-100 rounded-lg p-4 shadow-inner">
                {quizQuestions[currentQuestion]?.questionText}
              </div>
              <div className="space-y-4 mb-6">
                {quizQuestions[currentQuestion]?.answerOptions.map((answerOption: AnswerOption, index: number) => (
                  <button
                    key={index}
                    onClick={() => handleAnswerOptionClick(answerOption.isCorrect)}
                    className={`
                      w-full text-left py-3 px-4 rounded-xl border-2
                      transition duration-200 ease-in-out font-semibold text-lg
                      ${
                        selectedAnswer === null
                          ? 'bg-gray-100 hover:bg-gray-200 border-gray-300 text-gray-800'
                          : answerOption.isCorrect
                            ? 'bg-green-100 border-green-500 text-green-800'
                            : selectedAnswer !== null && !answerOption.isCorrect && selectedAnswer === false && selectedAnswer !== undefined
                              ? 'bg-red-100 border-red-500 text-red-800'
                              : 'bg-gray-100 border-gray-300 text-gray-800 opacity-70 cursor-not-allowed'
                      }
                      ${answersDisabled ? 'cursor-not-allowed' : 'cursor-pointer'}
                    `}
                    disabled={answersDisabled}
                  >
                    {answerOption.answerText}
                  </button>
                ))}
              </div>
              {feedbackMessage && (
                <div
                  className={`text-center text-lg font-bold mb-4
                    ${selectedAnswer ? 'text-green-600' : 'text-red-600'}
                  `}
                >
                  {feedbackMessage}
                </div>
              )}
              {selectedAnswer !== null && (
                <div className="mt-4">
                  <button
                    onClick={handleExplainAnswer}
                    className="w-full bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-3 px-6 rounded-xl transition duration-300 ease-in-out transform hover:scale-105 shadow-lg mb-4"
                    disabled={loadingExplanation}
                  >
                    {loadingExplanation ? 'Loading Explanation...' : '✨ Explain Answer'}
                  </button>
                  {explanation && (
                    <div className="bg-gray-50 p-4 rounded-lg text-gray-700 text-base border border-gray-200">
                      <h3 className="font-semibold text-base mb-2">Explanation:</h3>
                      <p>{explanation}</p>
                    </div>
                  )}
                </div>
              )}
              <button
                onClick={handleNextQuestion}
                className={`w-full font-bold py-3 px-6 rounded-xl transition duration-300 ease-in-out transform hover:scale-105 shadow-lg mt-6
                  ${selectedAnswer !== null ? 'bg-purple-600 hover:bg-purple-700 text-white' : 'bg-gray-300 text-gray-600 cursor-not-allowed'}
                `}
                disabled={selectedAnswer === null}
              >
                {currentQuestion === quizQuestions.length - 1 ? 'Finish Quiz' : 'Next Question'}
              </button>
            </div>
          )
        )}
      </div>
    </div>
  );
};

export default Quiz;