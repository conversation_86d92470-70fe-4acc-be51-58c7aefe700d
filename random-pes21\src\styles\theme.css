:root {
  /* Barcelona colors */
  --barça-red: #A50044;
  --barça-blue: #004D98;
  --dark-yellow: #f0ad00;
  --light-yellow: #ffdc5e;

  /* Background and text colors */
  --bg-color: #f5f5f5;
  --text-color: #333;

  /* Spacing */
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  /* Transitions */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

body {
  font-family: 'Roboto', 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Color Emoji', sans-serif;
  background-color: var(--bg-color);
  color: var(--text-color);
  margin: 0;
  padding: 0;
  line-height: 1.5;
}

/* Gradient utilities */
.gradient-red-blue {
  background: linear-gradient(90deg, var(--barça-red), var(--barça-blue));
  color: white;
}

.gradient-yellow {
  background: linear-gradient(90deg, var(--dark-yellow), var(--light-yellow));
  color: black;
}

.alternate-gradient-1 {
  background: linear-gradient(90deg, var(--barça-red) 0%, var(--barça-blue) 100%);
  color: white;
}

.alternate-gradient-2 {
  background: linear-gradient(90deg, var(--barça-blue) 0%, var(--barça-red) 100%);
  color: white;
}

/* Animation classes */
.fade-in {
  animation: fadeIn var(--transition-normal) forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dropdown styles */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown:hover .dropdown-content {
  display: block;
}

.dropdown-content {
  display: none;
  position: absolute;
  background-color: white;
  min-width: 160px;
  box-shadow: 0 8px 16px rgba(0,0,0,0.1);
  z-index: 1;
  border-radius: 8px;
  overflow: hidden;
}

.glowy-white-shadow {
  filter: drop-shadow(2px 2px 4px white) drop-shadow(1px 1px 3px white);
}

/* Emoji styling */
.emoji {
  font-family: 'Noto Color Emoji', sans-serif;
  font-size: 1.2em;
  display: inline-block;
  vertical-align: middle;
}